import { auth } from './lib/auth';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export default auth((req: NextRequest & { auth: { user?: { role?: string } } | null }) => {
  const { pathname } = req.nextUrl;

  // Public routes that don't require authentication
  const publicRoutes = ['/', '/search', '/auth/signin'];

  // Public route patterns (using regex for dynamic routes)
  const publicRoutePatterns = [
    /^\/results\/[^\/]+$/, // /results/[id]
    /^\/results\/[^\/]+\/progress$/, // /results/[id]/progress
    /^\/results\/[^\/]+\/feedback$/, // /results/[id]/feedback
    /^\/results\/[^\/]+\/certificate$/, // /results/[id]/certificate
    /^\/verify(\/[^\/]+)?$/, // /verify and /verify/[serial]
  ];

  // Admin-only routes
  const adminRoutes = ['/admin'];

  // Test checker routes (accessible by both admin and test_checker)
  const testCheckerRoutes = ['/dashboard'];

  // Allow public routes
  if (publicRoutes.includes(pathname)) {
    return NextResponse.next();
  }

  // Allow public route patterns
  if (publicRoutePatterns.some(pattern => pattern.test(pathname))) {
    return NextResponse.next();
  }

  // Redirect to signin if not authenticated
  if (!req.auth) {
    const signInUrl = new URL('/auth/signin', req.url);
    signInUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(signInUrl);
  }

  // Check admin routes
  if (adminRoutes.some(route => pathname.startsWith(route))) {
    if (req.auth.user?.role !== 'admin') {
      return NextResponse.redirect(new URL('/dashboard', req.url));
    }
  }

  // Check test checker routes
  if (testCheckerRoutes.some(route => pathname.startsWith(route))) {
    if (!req.auth.user?.role || !['admin', 'test_checker'].includes(req.auth.user.role)) {
      return NextResponse.redirect(new URL('/auth/signin', req.url));
    }
  }

  return NextResponse.next();
});

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
