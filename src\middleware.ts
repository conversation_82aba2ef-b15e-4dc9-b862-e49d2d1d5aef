import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export default function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/search',
    '/auth/signin',
    '/progress',
    '/feedback',
    '/certification'
  ];

  // Public route patterns (using regex for dynamic routes)
  const publicRoutePatterns = [
    /^\/results\/[^\/]+$/, // /results/[id]
    /^\/results\/[^\/]+\/progress$/, // /results/[id]/progress
    /^\/results\/[^\/]+\/feedback$/, // /results/[id]/feedback
    /^\/results\/[^\/]+\/certificate$/, // /results/[id]/certificate
    /^\/verify(\/[^\/]+)?$/, // /verify and /verify/[serial]
    /^\/api\//, // All API routes
    /^\/auth\//, // All auth routes
  ];

  // Allow public routes
  if (publicRoutes.includes(pathname)) {
    return NextResponse.next();
  }

  // Allow public route patterns
  if (publicRoutePatterns.some(pattern => pattern.test(pathname))) {
    return NextResponse.next();
  }

  // For protected routes, redirect to signin
  // In production, you would check authentication here
  // For demo purposes, we're allowing access to most routes
  const protectedRoutes = ['/admin', '/dashboard'];

  if (protectedRoutes.some(route => pathname.startsWith(route))) {
    // Check if user has a session cookie (simplified check)
    const sessionCookie = req.cookies.get('next-auth.session-token') || req.cookies.get('__Secure-next-auth.session-token');

    if (!sessionCookie) {
      const signInUrl = new URL('/auth/signin', req.url);
      signInUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(signInUrl);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
