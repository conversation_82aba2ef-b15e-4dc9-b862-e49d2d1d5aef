#!/usr/bin/env node

/**
 * Automated Vercel Deployment Script for IELTS Certification System
 * 
 * This script automates the deployment process to Vercel with proper
 * environment variable configuration and verification.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ ${description} failed:`, 'red');
    log(error.message, 'red');
    throw error;
  }
}

function checkPrerequisites() {
  log('\n📋 Checking prerequisites...', 'cyan');
  
  // Check if Vercel CLI is installed
  try {
    execSync('npx vercel --version', { stdio: 'pipe' });
    log('✅ Vercel CLI is available', 'green');
  } catch (error) {
    log('❌ Vercel CLI not found. Installing...', 'yellow');
    execCommand('npm install -g vercel', 'Installing Vercel CLI');
  }
  
  // Check if .env.local exists
  if (!fs.existsSync('.env.local')) {
    log('❌ .env.local file not found', 'red');
    log('Please create .env.local with required environment variables', 'yellow');
    process.exit(1);
  }
  
  log('✅ .env.local file found', 'green');
}

function verifyBuild() {
  log('\n🏗️ Verifying build process...', 'cyan');
  execCommand('npm run build', 'Building project');
}

function setupVercelProject() {
  log('\n🚀 Setting up Vercel project...', 'cyan');
  
  // Check if already linked to Vercel
  if (fs.existsSync('.vercel')) {
    log('✅ Project already linked to Vercel', 'green');
    return;
  }
  
  log('🔗 Linking project to Vercel...', 'blue');
  log('Please follow the prompts to link your project:', 'yellow');
  
  try {
    execSync('npx vercel', { stdio: 'inherit' });
    log('✅ Project linked to Vercel', 'green');
  } catch (error) {
    log('❌ Failed to link project to Vercel', 'red');
    throw error;
  }
}

function setEnvironmentVariables() {
  log('\n🔧 Setting up environment variables...', 'cyan');
  
  // Read .env.local file
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        envVars[key] = valueParts.join('=').replace(/^["']|["']$/g, '');
      }
    }
  });
  
  // Required environment variables
  const requiredVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'ANTHROPIC_API_KEY',
    'ADMIN_EMAIL',
    'ADMIN_PASSWORD'
  ];
  
  log('Setting environment variables in Vercel...', 'blue');
  
  for (const varName of requiredVars) {
    if (envVars[varName]) {
      try {
        // Check if variable already exists
        try {
          execSync(`npx vercel env ls | grep ${varName}`, { stdio: 'pipe' });
          log(`⚠️  ${varName} already exists, skipping...`, 'yellow');
        } catch {
          // Variable doesn't exist, add it
          execSync(`echo "${envVars[varName]}" | npx vercel env add ${varName} production`, { stdio: 'pipe' });
          log(`✅ Set ${varName}`, 'green');
        }
      } catch (error) {
        log(`❌ Failed to set ${varName}`, 'red');
      }
    } else {
      log(`⚠️  ${varName} not found in .env.local`, 'yellow');
    }
  }
  
  // Set NEXTAUTH_URL and NEXT_PUBLIC_APP_URL (will be updated after deployment)
  log('📝 Note: NEXTAUTH_URL and NEXT_PUBLIC_APP_URL will be set after deployment', 'yellow');
}

function deployToProduction() {
  log('\n🚀 Deploying to production...', 'cyan');
  
  try {
    const deployOutput = execSync('npx vercel --prod', { encoding: 'utf8', stdio: 'pipe' });
    
    // Extract deployment URL from output
    const urlMatch = deployOutput.match(/https:\/\/[^\s]+/);
    if (urlMatch) {
      const deploymentUrl = urlMatch[0];
      log(`✅ Deployment successful!`, 'green');
      log(`🌐 URL: ${deploymentUrl}`, 'bright');
      
      // Update NEXTAUTH_URL and NEXT_PUBLIC_APP_URL
      updateUrlEnvironmentVariables(deploymentUrl);
      
      return deploymentUrl;
    } else {
      log('✅ Deployment completed, but URL not found in output', 'yellow');
      log(deployOutput, 'reset');
    }
  } catch (error) {
    log('❌ Deployment failed:', 'red');
    log(error.message, 'red');
    throw error;
  }
}

function updateUrlEnvironmentVariables(deploymentUrl) {
  log('\n🔧 Updating URL environment variables...', 'cyan');
  
  try {
    // Update NEXTAUTH_URL
    execSync(`echo "${deploymentUrl}" | npx vercel env add NEXTAUTH_URL production`, { stdio: 'pipe' });
    log('✅ Updated NEXTAUTH_URL', 'green');
    
    // Update NEXT_PUBLIC_APP_URL
    execSync(`echo "${deploymentUrl}" | npx vercel env add NEXT_PUBLIC_APP_URL production`, { stdio: 'pipe' });
    log('✅ Updated NEXT_PUBLIC_APP_URL', 'green');
  } catch (error) {
    log('⚠️  Failed to update URL environment variables. Please update manually:', 'yellow');
    log(`   NEXTAUTH_URL=${deploymentUrl}`, 'yellow');
    log(`   NEXT_PUBLIC_APP_URL=${deploymentUrl}`, 'yellow');
  }
}

function verifyDeployment(deploymentUrl) {
  log('\n🔍 Verifying deployment...', 'cyan');
  
  if (!deploymentUrl) {
    log('⚠️  Deployment URL not available for verification', 'yellow');
    return;
  }
  
  log(`🌐 Testing ${deploymentUrl}...`, 'blue');
  
  // Basic connectivity test
  try {
    const https = require('https');
    const url = require('url');
    
    return new Promise((resolve, reject) => {
      const parsedUrl = url.parse(deploymentUrl);
      const options = {
        hostname: parsedUrl.hostname,
        port: 443,
        path: '/',
        method: 'GET',
        timeout: 10000
      };
      
      const req = https.request(options, (res) => {
        if (res.statusCode === 200) {
          log('✅ Homepage is accessible', 'green');
          resolve();
        } else {
          log(`⚠️  Homepage returned status ${res.statusCode}`, 'yellow');
          resolve();
        }
      });
      
      req.on('error', (error) => {
        log(`❌ Failed to access homepage: ${error.message}`, 'red');
        reject(error);
      });
      
      req.on('timeout', () => {
        log('⚠️  Homepage request timed out', 'yellow');
        req.destroy();
        resolve();
      });
      
      req.end();
    });
  } catch (error) {
    log(`⚠️  Could not verify deployment: ${error.message}`, 'yellow');
  }
}

function printSummary(deploymentUrl) {
  log('\n🎉 Deployment Summary', 'bright');
  log('═'.repeat(50), 'cyan');
  
  if (deploymentUrl) {
    log(`🌐 Application URL: ${deploymentUrl}`, 'green');
  }
  
  log('\n📋 Demo Credentials:', 'cyan');
  log('   Admin: <EMAIL> / admin123', 'yellow');
  log('   Test Checker: <EMAIL> / checker123', 'yellow');
  
  log('\n✅ Deployment completed successfully!', 'green');
  log('\n📝 Next steps:', 'cyan');
  log('   1. Test the application with demo credentials', 'reset');
  log('   2. Verify database connectivity', 'reset');
  log('   3. Configure custom domain (optional)', 'reset');
  log('   4. Set up monitoring and analytics', 'reset');
  
  log('\n📚 For troubleshooting, see VERCEL_DEPLOYMENT_GUIDE.md', 'blue');
}

async function main() {
  try {
    log('🚀 IELTS Certification System - Vercel Deployment', 'bright');
    log('═'.repeat(60), 'cyan');
    
    checkPrerequisites();
    verifyBuild();
    setupVercelProject();
    setEnvironmentVariables();
    
    const deploymentUrl = deployToProduction();
    
    if (deploymentUrl) {
      await verifyDeployment(deploymentUrl);
    }
    
    printSummary(deploymentUrl);
    
  } catch (error) {
    log('\n❌ Deployment failed:', 'red');
    log(error.message, 'red');
    log('\n📚 Check VERCEL_DEPLOYMENT_GUIDE.md for troubleshooting', 'blue');
    process.exit(1);
  }
}

// Run the deployment script
if (require.main === module) {
  main();
}

module.exports = { main };
